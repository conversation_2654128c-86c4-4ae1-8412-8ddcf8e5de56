@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700&family=Noto+Serif+SC:wght@400;600&display=swap');

:root {
    --bg-image: url('https://www.transparenttextures.com/patterns/wood-pattern.png');
    --paper-bg: #fdf6e3; /* Parchment color */
    --paper-bg-light: #fefbf0; /* Lighter parchment */
    --ink-color: #583e2e; /* Dark brown ink */
    --ink-light: #6b4d3e; /* Lighter brown */
    --heading-color: #8b0000; /* Dark red */
    --heading-hover: #a50000; /* Brighter red for hover */
    --accent-color: #c8a464; /* Old gold */
    --accent-light: #d4b373; /* Lighter gold */
    --accent-dark: #b8945a; /* Darker gold */
    --border-color: #d9c8a9;
    --border-light: #e5d4b5;
    --shadow-light: rgba(0,0,0,0.1);
    --shadow-medium: rgba(0,0,0,0.2);
    --shadow-dark: rgba(0,0,0,0.3);
}

html {
    scroll-behavior: smooth;
}

body {
    margin: 0;
    padding: 0;
    background-color: #3a302a;
    background-image: var(--bg-image);
    font-family: 'Noto Serif SC', serif;
    color: var(--ink-color);
    line-height: 1.9;
}

.library-desk {
    max-width: 1000px;
    margin: 50px auto;
    padding: 50px 70px;
    background: linear-gradient(145deg, var(--paper-bg), var(--paper-bg-light));
    border: 2px solid #4a382a;
    border-radius: 8px;
    box-shadow:
        0 0 40px var(--shadow-dark),
        inset 0 0 30px var(--shadow-medium),
        0 20px 40px var(--shadow-light);
    position: relative;
    backdrop-filter: blur(10px);
}

.library-desk::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background-image: url('https://www.transparenttextures.com/patterns/worn-dots.png');
    opacity: 0.08;
    pointer-events: none;
}

header {
    text-align: center;
    margin-bottom: 60px;
    border-bottom: 3px double var(--border-color);
    padding-bottom: 30px;
}

header h1 {
    font-family: 'Playfair Display', serif;
    font-size: 3.5em;
    color: var(--heading-color);
    margin: 0;
    text-shadow:
        2px 2px 4px var(--shadow-light),
        0 0 10px rgba(139, 0, 0, 0.3);
    transition: all 0.3s ease;
    cursor: default;
}

header h1:hover {
    color: var(--heading-hover);
    text-shadow:
        2px 2px 6px var(--shadow-medium),
        0 0 15px rgba(139, 0, 0, 0.5);
    transform: scale(1.02);
}

header p {
    font-size: 1.2em;
    color: var(--ink-color);
    max-width: 700px;
    margin: 15px auto 0;
    font-style: italic;
}

.category {
    margin-bottom: 50px;
}

.category h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.6em;
    color: var(--heading-color);
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 15px;
    margin-bottom: 35px;
    font-weight: 700;
    position: relative;
    transition: all 0.3s ease;
    cursor: default;
}

.category h2:hover {
    color: var(--heading-hover);
    transform: translateX(5px);
}

.category h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
    transition: width 0.3s ease;
}

.category h2:hover::after {
    width: 100px;
}

.book-entry {
    margin-bottom: 40px;
    padding: 20px 25px;
    border-left: 4px solid var(--accent-color);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0 5px 5px 0;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

.book-entry:nth-child(even) {
    animation-delay: 0.1s;
}

.book-entry:nth-child(odd) {
    animation-delay: 0.2s;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.book-entry:hover {
    background: linear-gradient(135deg, rgba(200, 164, 100, 0.08), rgba(200, 164, 100, 0.03));
    border-left-width: 6px;
    transform: translateX(5px) translateY(-2px);
    box-shadow: 4px 6px 16px rgba(0,0,0,0.15);
}

.book-entry:hover h3 {
    color: var(--heading-color);
    transform: translateX(2px);
}

.book-entry:hover .recommendation {
    background: linear-gradient(135deg, rgba(253, 246, 227, 0.8), rgba(217, 200, 169, 0.3));
    transform: translateX(2px);
}

.book-entry h3 {
    font-family: 'Noto Serif SC', serif;
    font-weight: 600;
    font-size: 1.8em;
    margin: 0 0 15px 0;
    color: #3d2c21;
    letter-spacing: 0.5px;
    text-shadow: 0.5px 0.5px 1px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.book-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px 25px;
    margin-bottom: 18px;
    font-size: 0.95em;
    color: var(--ink-color);
    background: rgba(200, 164, 100, 0.08);
    padding: 12px 15px;
    border-radius: 3px;
    border-left: 2px solid var(--accent-color);
}

.book-meta span {
    font-weight: 600;
}

.reading-time {
    color: var(--accent-color);
    font-style: italic;
}

.book-entry .recommendation {
    font-size: 1.1em;
    text-indent: 2em;
    line-height: 1.8;
    margin-top: 20px;
    padding: 15px 20px;
    background: linear-gradient(135deg, rgba(253, 246, 227, 0.6), rgba(217, 200, 169, 0.2));
    border-radius: 5px;
    border-left: 3px solid var(--accent-color);
    font-style: italic;
    transition: all 0.3s ease;
}

/* Focus styles for better accessibility */
*:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

button:focus {
    outline-style: solid;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--accent-color);
    color: var(--paper-bg);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: var(--heading-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.4);
}

/* Loading Indicator */


footer {
    text-align: center;
    margin-top: 50px;
    padding-top: 30px;
    border-top: 3px double var(--border-color);
    font-style: italic;
    color: var(--ink-color);
}



/* Responsive Design */
@media screen and (max-width: 1200px) {
    .library-desk {
        max-width: 900px;
        padding: 40px 50px;
        margin: 30px auto;
    }

    header h1 {
        font-size: 3em;
    }

    .category h2 {
        font-size: 2.3em;
    }
}

@media screen and (max-width: 768px) {
    .library-desk {
        max-width: 95%;
        padding: 30px 25px;
        margin: 20px auto;
        box-shadow: 0 0 20px rgba(0,0,0,0.4), inset 0 0 15px rgba(0,0,0,0.1);
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }

    header {
        margin-bottom: 40px;
        padding-bottom: 20px;
    }

    header h1 {
        font-size: 2.5em;
        line-height: 1.2;
    }

    header p {
        font-size: 1.1em;
        margin: 10px auto 0;
    }

    .category {
        margin-bottom: 35px;
    }

    .category h2 {
        font-size: 2em;
        margin-bottom: 25px;
        padding-bottom: 10px;
    }

    .book-entry {
        margin-bottom: 30px;
        padding: 15px 20px;
        border-left-width: 3px;
    }

    .book-entry:hover {
        border-left-width: 4px;
        transform: translateX(2px);
    }

    .book-entry h3 {
        font-size: 1.6em;
        margin-bottom: 12px;
    }

    .book-meta {
        grid-template-columns: 1fr;
        gap: 6px;
        margin-bottom: 15px;
        padding: 10px 12px;
        font-size: 0.9em;
    }

    .book-entry .recommendation {
        font-size: 1em;
        padding: 12px 15px;
        margin-top: 15px;
        text-indent: 1.5em;
    }

    footer {
        margin-top: 35px;
        padding-top: 20px;
        font-size: 0.95em;
    }
}

@media screen and (max-width: 480px) {
    .library-desk {
        padding: 20px 15px;
        margin: 10px auto;
    }

    .back-to-top {
        bottom: 15px;
        right: 15px;
        width: 40px;
        height: 40px;
    }

    header h1 {
        font-size: 2.2em;
    }

    header p {
        font-size: 1em;
    }

    .category h2 {
        font-size: 1.8em;
    }

    .book-entry {
        padding: 12px 15px;
    }

    .book-entry h3 {
        font-size: 1.4em;
    }

    .book-meta {
        padding: 8px 10px;
        font-size: 0.85em;
    }

    .book-entry .recommendation {
        font-size: 0.95em;
        padding: 10px 12px;
        text-indent: 1em;
        line-height: 1.7;
    }
}

