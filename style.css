@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700&family=Noto+Serif+SC:wght@400;600&display=swap');

:root {
    --bg-image: url('https://www.transparenttextures.com/patterns/wood-pattern.png');
    --paper-bg: #fdf6e3; /* Parchment color */
    --ink-color: #583e2e; /* Dark brown ink */
    --heading-color: #8b0000; /* Dark red */
    --accent-color: #c8a464; /* Old gold */
    --border-color: #d9c8a9;
}

html {
    scroll-behavior: smooth;
}

body {
    margin: 0;
    padding: 0;
    background-color: #3a302a;
    background-image: var(--bg-image);
    font-family: 'Noto Serif SC', serif;
    color: var(--ink-color);
    line-height: 1.9;
}

.library-desk {
    max-width: 1000px;
    margin: 50px auto;
    padding: 50px 70px;
    background-color: var(--paper-bg);
    border: 2px solid #4a382a;
    border-radius: 5px;
    box-shadow: 0 0 30px rgba(0,0,0,0.6), inset 0 0 25px rgba(0,0,0,0.2);
    position: relative;
}

.library-desk::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background-image: url('https://www.transparenttextures.com/patterns/worn-dots.png');
    opacity: 0.08;
    pointer-events: none;
}

header {
    text-align: center;
    margin-bottom: 60px;
    border-bottom: 3px double var(--border-color);
    padding-bottom: 30px;
}

header h1 {
    font-family: 'Playfair Display', serif;
    font-size: 3.5em;
    color: var(--heading-color);
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

header p {
    font-size: 1.2em;
    color: var(--ink-color);
    max-width: 700px;
    margin: 15px auto 0;
    font-style: italic;
}

.category {
    margin-bottom: 50px;
}

.category h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.6em;
    color: var(--heading-color);
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 15px;
    margin-bottom: 35px;
    font-weight: 700;
    position: relative;
}

.category h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--accent-color);
}

.book-entry {
    margin-bottom: 40px;
    padding: 20px 25px;
    border-left: 4px solid var(--accent-color);
    transition: all 0.3s ease;
    border-radius: 0 5px 5px 0;
}

.book-entry:hover {
    background: rgba(200, 164, 100, 0.05);
    border-left-width: 6px;
    transform: translateX(3px);
    box-shadow: 2px 2px 8px rgba(0,0,0,0.1);
}

.book-entry h3 {
    font-family: 'Noto Serif SC', serif;
    font-weight: 600;
    font-size: 1.8em;
    margin: 0 0 15px 0;
    color: #3d2c21;
    letter-spacing: 0.5px;
    text-shadow: 0.5px 0.5px 1px rgba(0,0,0,0.1);
}

.book-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px 25px;
    margin-bottom: 18px;
    font-size: 0.95em;
    color: var(--ink-color);
    background: rgba(200, 164, 100, 0.08);
    padding: 12px 15px;
    border-radius: 3px;
    border-left: 2px solid var(--accent-color);
}

.book-meta span {
    font-weight: 600;
}

.book-entry .recommendation {
    font-size: 1.1em;
    text-indent: 2em;
    line-height: 1.8;
    margin-top: 20px;
    padding: 15px 20px;
    background: linear-gradient(135deg, rgba(253, 246, 227, 0.6), rgba(217, 200, 169, 0.2));
    border-radius: 5px;
    border-left: 3px solid var(--accent-color);
    font-style: italic;
}

footer {
    text-align: center;
    margin-top: 50px;
    padding-top: 30px;
    border-top: 3px double var(--border-color);
    font-style: italic;
    color: var(--ink-color);
}

