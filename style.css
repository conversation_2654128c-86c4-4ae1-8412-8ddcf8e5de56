@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700&family=Noto+Serif+SC:wght@400;600&display=swap');

:root {
    --bg-image: url('https://www.transparenttextures.com/patterns/wood-pattern.png');
    --paper-bg: #fdf6e3; /* Parchment color */
    --paper-bg-light: #fefbf0; /* Lighter parchment */
    --ink-color: #583e2e; /* Dark brown ink */
    --ink-light: #6b4d3e; /* Lighter brown */
    --heading-color: #8b0000; /* Dark red */
    --heading-hover: #a50000; /* Brighter red for hover */
    --accent-color: #c8a464; /* Old gold */
    --accent-light: #d4b373; /* Lighter gold */
    --accent-dark: #b8945a; /* Darker gold */
    --border-color: #d9c8a9;
    --border-light: #e5d4b5;
    --shadow-light: rgba(0,0,0,0.1);
    --shadow-medium: rgba(0,0,0,0.2);
    --shadow-dark: rgba(0,0,0,0.3);
}

html {
    scroll-behavior: smooth;
}

/* Subtle animations for enhanced visual experience */
@keyframes gentleFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes pageLoad {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.library-desk {
    animation:
        pageLoad 1s ease-out,
        gentleFloat 6s ease-in-out infinite 1s;
}

/* Add shimmer effect to accent elements */
.category h2::after,
.book-entry h3::after {
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

body {
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #3a302a 0%, #2d2520 50%, #3a302a 100%);
    background-image: var(--bg-image);
    background-attachment: fixed;
    font-family: 'Noto Serif SC', serif;
    color: var(--ink-color);
    line-height: 1.9;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.library-desk {
    max-width: 1000px;
    margin: 50px auto;
    padding: 60px 80px;
    background:
        radial-gradient(circle at 20% 20%, rgba(253, 246, 227, 0.95) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(217, 200, 169, 0.3) 0%, transparent 50%),
        linear-gradient(145deg, var(--paper-bg) 0%, var(--paper-bg-light) 100%);
    border: 3px solid #4a382a;
    border-radius: 12px;
    box-shadow:
        0 0 60px rgba(0,0,0,0.4),
        inset 0 0 40px rgba(0,0,0,0.15),
        0 25px 50px rgba(0,0,0,0.2),
        inset 0 1px 0 rgba(255,255,255,0.1);
    position: relative;
    backdrop-filter: blur(10px);
}

.library-desk::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background-image:
        url('https://www.transparenttextures.com/patterns/worn-dots.png'),
        radial-gradient(circle at 30% 40%, rgba(200, 164, 100, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 60%, rgba(139, 0, 0, 0.03) 0%, transparent 50%);
    opacity: 0.12;
    pointer-events: none;
    border-radius: 12px;
}

.library-desk::after {
    content: '';
    position: absolute;
    top: -2px; left: -2px; right: -2px; bottom: -2px;
    background: linear-gradient(45deg,
        rgba(200, 164, 100, 0.3) 0%,
        transparent 25%,
        transparent 75%,
        rgba(200, 164, 100, 0.3) 100%);
    border-radius: 14px;
    z-index: -1;
}

header {
    text-align: center;
    margin-bottom: 60px;
    border-bottom: 3px double var(--border-color);
    padding-bottom: 30px;
}

header h1 {
    font-family: 'Playfair Display', serif;
    font-size: 3.8em;
    font-weight: 700;
    color: var(--heading-color);
    margin: 0;
    text-shadow:
        3px 3px 6px rgba(0,0,0,0.3),
        0 0 20px rgba(139, 0, 0, 0.4),
        1px 1px 0 rgba(255,255,255,0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: default;
    letter-spacing: 1px;
    background: linear-gradient(135deg, var(--heading-color) 0%, #a50000 50%, var(--heading-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

header h1::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    color: var(--heading-color);
    z-index: -1;
    text-shadow:
        3px 3px 6px rgba(0,0,0,0.3),
        0 0 20px rgba(139, 0, 0, 0.4);
}

header h1:hover {
    transform: scale(1.03) translateY(-2px);
    text-shadow:
        4px 4px 8px rgba(0,0,0,0.4),
        0 0 25px rgba(139, 0, 0, 0.6),
        2px 2px 0 rgba(255,255,255,0.15);
}

header p {
    font-size: 1.3em;
    color: var(--ink-light);
    max-width: 750px;
    margin: 25px auto 0;
    font-style: italic;
    line-height: 1.6;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    background: linear-gradient(135deg, var(--ink-color) 0%, var(--ink-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

header p::before {
    content: '"';
    position: absolute;
    left: -20px;
    top: -5px;
    font-size: 2em;
    color: var(--accent-color);
    opacity: 0.6;
}

header p::after {
    content: '"';
    position: absolute;
    right: -20px;
    bottom: -10px;
    font-size: 2em;
    color: var(--accent-color);
    opacity: 0.6;
}

.category {
    margin-bottom: 50px;
}

.category h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.8em;
    font-weight: 700;
    color: var(--heading-color);
    border-bottom: 3px solid transparent;
    background: linear-gradient(var(--paper-bg), var(--paper-bg)) padding-box,
                linear-gradient(90deg, var(--border-color), var(--accent-color), var(--border-color)) border-box;
    border-bottom: 3px solid transparent;
    padding-bottom: 18px;
    margin-bottom: 40px;
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: default;
    text-shadow:
        2px 2px 4px rgba(0,0,0,0.2),
        0 0 15px rgba(139, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.category h2:hover {
    color: var(--heading-hover);
    transform: translateX(8px) translateY(-2px);
    text-shadow:
        3px 3px 6px rgba(0,0,0,0.3),
        0 0 20px rgba(139, 0, 0, 0.5);
}

.category h2::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -10px;
    right: -10px;
    bottom: -5px;
    background: linear-gradient(135deg,
        rgba(200, 164, 100, 0.1) 0%,
        transparent 50%,
        rgba(139, 0, 0, 0.05) 100%);
    border-radius: 8px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category h2:hover::before {
    opacity: 1;
}

.category h2::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg,
        var(--accent-color) 0%,
        var(--accent-light) 50%,
        var(--accent-dark) 100%);
    border-radius: 2px;
    transition: all 0.4s ease;
    box-shadow: 0 2px 4px rgba(200, 164, 100, 0.3);
}

.category h2:hover::after {
    width: 120px;
    box-shadow: 0 3px 8px rgba(200, 164, 100, 0.5);
}

.book-entry {
    margin-bottom: 45px;
    padding: 25px 30px;
    border-left: 5px solid transparent;
    background:
        linear-gradient(var(--paper-bg), var(--paper-bg)) padding-box,
        linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 50%, var(--accent-dark) 100%) border-box;
    border-left: 5px solid transparent;
    border-radius: 0 8px 8px 0;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease forwards;
    position: relative;
    box-shadow:
        0 2px 8px rgba(0,0,0,0.08),
        inset 0 1px 0 rgba(255,255,255,0.1);
}

.book-entry:nth-child(even) {
    animation-delay: 0.15s;
}

.book-entry:nth-child(odd) {
    animation-delay: 0.3s;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.book-entry::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(200, 164, 100, 0.12) 0%,
        rgba(253, 246, 227, 0.05) 50%,
        rgba(200, 164, 100, 0.08) 100%);
    border-radius: 0 8px 8px 0;
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.book-entry:hover::before {
    opacity: 1;
}

.book-entry:hover {
    border-left-width: 8px;
    transform: translateX(8px) translateY(-4px);
    box-shadow:
        6px 10px 25px rgba(0,0,0,0.2),
        inset 0 1px 0 rgba(255,255,255,0.2),
        0 0 20px rgba(200, 164, 100, 0.15);
}

.book-entry:hover h3 {
    color: var(--heading-hover);
    transform: translateX(3px);
    text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
}

.book-entry:hover .recommendation {
    transform: translateX(3px);
    box-shadow: inset 0 1px 0 rgba(255,255,255,0.2);
}

.book-entry h3 {
    font-family: 'Noto Serif SC', serif;
    font-weight: 600;
    font-size: 1.9em;
    margin: 0 0 18px 0;
    color: #3d2c21;
    letter-spacing: 0.8px;
    text-shadow:
        1px 1px 2px rgba(0,0,0,0.15),
        0 0 8px rgba(61, 44, 33, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    line-height: 1.3;
}

.book-entry h3::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
    transition: width 0.4s ease;
    border-radius: 1px;
}

.book-entry:hover h3::after {
    width: 60px;
}

.book-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px 30px;
    margin-bottom: 22px;
    font-size: 1em;
    color: var(--ink-color);
    background:
        linear-gradient(135deg,
            rgba(200, 164, 100, 0.12) 0%,
            rgba(253, 246, 227, 0.08) 50%,
            rgba(200, 164, 100, 0.06) 100%);
    padding: 16px 20px;
    border-radius: 6px;
    border-left: 3px solid var(--accent-color);
    box-shadow:
        inset 0 1px 0 rgba(255,255,255,0.1),
        0 1px 3px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.book-entry:hover .book-meta {
    background:
        linear-gradient(135deg,
            rgba(200, 164, 100, 0.18) 0%,
            rgba(253, 246, 227, 0.12) 50%,
            rgba(200, 164, 100, 0.10) 100%);
    box-shadow:
        inset 0 1px 0 rgba(255,255,255,0.15),
        0 2px 6px rgba(0,0,0,0.08);
}

.book-meta span {
    font-weight: 600;
    color: var(--ink-light);
}

.reading-time {
    color: var(--accent-dark);
    font-style: italic;
    font-weight: 500;
}

.book-entry .recommendation {
    font-size: 1.15em;
    text-indent: 2.5em;
    line-height: 1.9;
    margin-top: 25px;
    padding: 20px 25px;
    background:
        linear-gradient(135deg,
            rgba(253, 246, 227, 0.8) 0%,
            rgba(217, 200, 169, 0.4) 50%,
            rgba(253, 246, 227, 0.6) 100%);
    border-radius: 8px;
    border-left: 4px solid var(--accent-color);
    border-top: 1px solid rgba(200, 164, 100, 0.3);
    font-style: italic;
    transition: all 0.4s ease;
    position: relative;
    box-shadow:
        inset 0 1px 0 rgba(255,255,255,0.2),
        0 2px 6px rgba(0,0,0,0.08);
    color: var(--ink-light);
}

.book-entry .recommendation::before {
    content: '"';
    position: absolute;
    top: 8px;
    left: 15px;
    font-size: 2.5em;
    color: var(--accent-color);
    opacity: 0.4;
    font-family: 'Playfair Display', serif;
    line-height: 1;
}

.book-entry .recommendation::after {
    content: '"';
    position: absolute;
    bottom: 5px;
    right: 20px;
    font-size: 2.5em;
    color: var(--accent-color);
    opacity: 0.4;
    font-family: 'Playfair Display', serif;
    line-height: 1;
}

.book-entry:hover .recommendation {
    background:
        linear-gradient(135deg,
            rgba(253, 246, 227, 0.95) 0%,
            rgba(217, 200, 169, 0.6) 50%,
            rgba(253, 246, 227, 0.8) 100%);
    box-shadow:
        inset 0 1px 0 rgba(255,255,255,0.3),
        0 3px 10px rgba(0,0,0,0.12);
    border-left-color: var(--accent-dark);
}

/* Focus styles for better accessibility */
*:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(200, 164, 100, 0.2);
}

button:focus {
    outline-style: solid;
}

/* Add subtle glow effects */
.library-desk:hover {
    box-shadow:
        0 0 80px rgba(0,0,0,0.4),
        inset 0 0 50px rgba(0,0,0,0.15),
        0 30px 60px rgba(0,0,0,0.2),
        inset 0 1px 0 rgba(255,255,255,0.15),
        0 0 100px rgba(200, 164, 100, 0.1);
}

/* Enhance text selection */
::selection {
    background: rgba(200, 164, 100, 0.3);
    color: var(--heading-color);
}

::-moz-selection {
    background: rgba(200, 164, 100, 0.3);
    color: var(--heading-color);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 35px;
    right: 35px;
    width: 55px;
    height: 55px;
    background:
        linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%);
    color: var(--paper-bg);
    border: 2px solid rgba(255,255,255,0.2);
    border-radius: 50%;
    cursor: pointer;
    box-shadow:
        0 6px 20px rgba(0,0,0,0.3),
        inset 0 1px 0 rgba(255,255,255,0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(30px) scale(0.8);
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.back-to-top:hover {
    background:
        linear-gradient(135deg, var(--heading-color) 0%, #a50000 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow:
        0 10px 30px rgba(0,0,0,0.4),
        inset 0 1px 0 rgba(255,255,255,0.3),
        0 0 20px rgba(139, 0, 0, 0.3);
    border-color: rgba(255,255,255,0.3);
}

.back-to-top svg {
    transition: transform 0.3s ease;
}

.back-to-top:hover svg {
    transform: translateY(-2px);
}

/* Loading Indicator */


footer {
    text-align: center;
    margin-top: 60px;
    padding: 40px 0 20px;
    border-top: 3px double var(--border-color);
    font-style: italic;
    color: var(--ink-light);
    position: relative;
    background:
        linear-gradient(135deg,
            rgba(200, 164, 100, 0.05) 0%,
            transparent 50%,
            rgba(139, 0, 0, 0.03) 100%);
    border-radius: 8px 8px 0 0;
}

footer::before {
    content: '';
    position: absolute;
    top: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--accent-color) 50%,
        transparent 100%);
    border-radius: 2px;
}

footer p {
    font-size: 1.1em;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    letter-spacing: 0.5px;
}



/* Responsive Design */
@media screen and (max-width: 1200px) {
    .library-desk {
        max-width: 900px;
        padding: 40px 50px;
        margin: 30px auto;
    }

    header h1 {
        font-size: 3em;
    }

    .category h2 {
        font-size: 2.3em;
    }
}

@media screen and (max-width: 768px) {
    .library-desk {
        max-width: 95%;
        padding: 30px 25px;
        margin: 20px auto;
        box-shadow: 0 0 20px rgba(0,0,0,0.4), inset 0 0 15px rgba(0,0,0,0.1);
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }

    header {
        margin-bottom: 40px;
        padding-bottom: 20px;
    }

    header h1 {
        font-size: 2.5em;
        line-height: 1.2;
    }

    header p {
        font-size: 1.1em;
        margin: 10px auto 0;
    }

    .category {
        margin-bottom: 35px;
    }

    .category h2 {
        font-size: 2em;
        margin-bottom: 25px;
        padding-bottom: 10px;
    }

    .book-entry {
        margin-bottom: 30px;
        padding: 15px 20px;
        border-left-width: 3px;
    }

    .book-entry:hover {
        border-left-width: 4px;
        transform: translateX(2px);
    }

    .book-entry h3 {
        font-size: 1.6em;
        margin-bottom: 12px;
    }

    .book-meta {
        grid-template-columns: 1fr;
        gap: 6px;
        margin-bottom: 15px;
        padding: 10px 12px;
        font-size: 0.9em;
    }

    .book-entry .recommendation {
        font-size: 1em;
        padding: 12px 15px;
        margin-top: 15px;
        text-indent: 1.5em;
    }

    footer {
        margin-top: 35px;
        padding-top: 20px;
        font-size: 0.95em;
    }
}

@media screen and (max-width: 480px) {
    .library-desk {
        padding: 20px 15px;
        margin: 10px auto;
    }

    .back-to-top {
        bottom: 15px;
        right: 15px;
        width: 40px;
        height: 40px;
    }

    header h1 {
        font-size: 2.2em;
    }

    header p {
        font-size: 1em;
    }

    .category h2 {
        font-size: 1.8em;
    }

    .book-entry {
        padding: 12px 15px;
    }

    .book-entry h3 {
        font-size: 1.4em;
    }

    .book-meta {
        padding: 8px 10px;
        font-size: 0.85em;
    }

    .book-entry .recommendation {
        font-size: 0.95em;
        padding: 10px 12px;
        text-indent: 1em;
        line-height: 1.7;
    }
}

