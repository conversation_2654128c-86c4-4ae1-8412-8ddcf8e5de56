<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度思考者的书架</title>
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <!-- Skip link for accessibility -->
    <a href="#main-content" class="skip-link">跳转到主要内容</a>

    <div class="library-desk" role="main">
        <header>
            <nav aria-label="面包屑导航" class="breadcrumb">
                <ol>
                    <li><a href="#" aria-current="page">深度思考者的书架</a></li>
                </ol>
            </nav>
            <h1 id="main-title"></h1>
            <p id="intro-quote"></p>
        </header>

        <!-- Search and Filter Controls -->
        <section class="controls-section" aria-label="搜索和筛选控件">
            <div class="search-container">
                <label for="search-input" class="visually-hidden">搜索书籍</label>
                <input type="text" id="search-input" placeholder="搜索书籍、作者或关键词..." aria-label="搜索书籍">
                <button id="search-clear" class="clear-btn" aria-label="清除搜索" tabindex="-1">×</button>
            </div>

            <div class="filter-container" role="group" aria-label="筛选选项">
                <label for="category-filter" class="visually-hidden">按分类筛选</label>
                <select id="category-filter" aria-label="按分类筛选">
                    <option value="">所有分类</option>
                </select>

                <label for="year-filter" class="visually-hidden">按年代筛选</label>
                <select id="year-filter" aria-label="按年代筛选">
                    <option value="">所有年代</option>
                    <option value="1600-1799">17-18世纪</option>
                    <option value="1800-1899">19世纪</option>
                    <option value="1900-1949">20世纪前半</option>
                    <option value="1950-1999">20世纪后半</option>
                    <option value="2000-2099">21世纪</option>
                </select>
            </div>

            <div class="results-info" role="status" aria-live="polite">
                <span id="results-count"></span>
            </div>
        </section>

        <main id="main-content" id="book-list" aria-label="书籍列表">
            <!-- Book categories and entries will be generated by JavaScript -->
        </main>

        <footer>
            <p id="footer-text"></p>
        </footer>
    </div>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="back-to-top" aria-label="返回顶部" title="返回顶部">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="m18 15-6-6-6 6" />
        </svg>
    </button>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="loading-indicator">
        <div class="loading-spinner"></div>
        <p>正在加载书籍...</p>
    </div>

    <!-- Book Detail Modal -->
    <div id="book-modal" class="modal-overlay">
        <div class="modal-content">
            <button class="modal-close" aria-label="关闭详情">&times;</button>
            <div class="modal-header">
                <h2 id="modal-title"></h2>
                <p id="modal-original-title"></p>
            </div>
            <div class="modal-body">
                <div class="modal-meta">
                    <div class="meta-item">
                        <strong>著者:</strong> <span id="modal-author"></span>
                    </div>
                    <div class="meta-item">
                        <strong>国籍:</strong> <span id="modal-nationality"></span>
                    </div>
                    <div class="meta-item">
                        <strong>初版年份:</strong> <span id="modal-year"></span>
                    </div>
                    <div class="meta-item">
                        <strong>分类:</strong> <span id="modal-category"></span>
                    </div>
                </div>
                <div class="modal-recommendation">
                    <h3>推荐理由</h3>
                    <p id="modal-recommendation-text"></p>
                </div>
                <div class="modal-actions">
                    <button id="favorite-btn" class="action-btn favorite-btn">
                        <span class="heart">♡</span> 收藏
                    </button>
                    <button id="reading-btn" class="action-btn reading-btn">
                        📖 标记为在读
                    </button>
                    <button id="share-btn" class="action-btn share-btn">
                        🔗 分享
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>

</html>