document.addEventListener('DOMContentLoaded', () => {

    const bookData = {
        title: "深度思考者的书架",
        quote: "“Hic sunt dracones.” (此处有龙) - 古地图上标记未知区域的拉丁语，象征着探索思想的未知领域。",
        footer: "In libris, libertas. (在书中，有自由。)",
        categories: [
            {
                name: "经济与政治经济学",
                books: [
                    { title: "国富论", originalTitle: "The Wealth of Nations", author: "亚当·斯密", nationality: "苏格兰", year: 1776, recommendation: "现代经济学的奠基之作，系统阐述了市场经济、劳动分工和“看不见的手”等核心概念，是理解资本主义运作方式的起点。" },
                    { title: "人类行为", originalTitle: "Human Action: A Treatise on Economics", author: "路德维希·冯·米塞斯", nationality: "奥地利裔美国人", year: 1949, recommendation: "奥地利学派的巨著，从“人是有目的地行动”这一公理出发，构建了整个经济学大厦，是自由意志主义经济学的思想基石。" },
                    { title: "经济学入门", originalTitle: "Economics in One Lesson", author: "亨利·黑兹利特", nationality: "美国", year: 1946, recommendation: "自由市场经济学的最佳入门指南，用“关注长远和对所有群体的影响”这一核心原则，剖析了常见的经济谬误。" },
                    { title: "资本主义与自由", originalTitle: "Capitalism and Freedom", author: "米尔顿·弗里德曼", nationality: "美国", year: 1962, recommendation: "系统地论证了经济自由与政治自由之间不可分割的关系，并将古典自由主义的原则应用于解决现代社会问题。" },
                    { title: "自由选择", originalTitle: "Free to Choose", author: "米尔顿 & 罗丝·弗里德曼", nationality: "美国", year: 1980, recommendation: "弗里德曼夫妇的通俗杰作，用平实的语言和生动的案例，向公众阐明了自由市场在促进繁荣和自由方面的力量。" }
                ]
            },
            {
                name: "政治哲学与社会学",
                books: [
                    { title: "论法的精神", originalTitle: "De l'esprit des lois", author: "孟德斯鸠", nationality: "法国", year: 1748, recommendation: "深刻影响了启蒙思想家和现代政治制度的巨著。其“三权分立”学说是现代宪政的基石。" },
                    { title: "政府论", originalTitle: "Two Treatises of Government", author: "约翰·洛克", nationality: "英国", year: 1689, recommendation: "现代自由主义的奠基文献，系统阐述了生命、自由和财产权等自然权利，主张政府的合法性源于被统治者的同意。" },
                    { title: "论自由", originalTitle: "On Liberty", author: "约翰·斯图尔特·密尔", nationality: "英国", year: 1859, recommendation: "将“自由”的讨论从经济领域扩展到了思想、言论和个人生活的方方面面，其“伤害原则”是现代自由主义的核心信条。" },
                    { title: "社会契约论", originalTitle: "Du Contrat Social", author: "让-雅克·卢梭", nationality: "日内瓦", year: 1762, recommendation: "探讨了“主权在民”和“公意”等概念，是现代民主思想的源头之一。" },
                    { title: "联邦党人文集", originalTitle: "The Federalist Papers", author: "汉密尔ton、麦迪逊、杰伊", nationality: "美国", year: 1788, recommendation: "理解美国宪法和共和制设计理念的必读之作，展示了如何将启蒙思想付诸实践。" },
                    { title: "论美国的民主", originalTitle: "De la démocratie en Amérique", author: "亚历西斯·德·托克维尔", nationality: "法国", year: 1835, recommendation: "对新兴民主社会的深刻洞察，分析了民主的优势与潜在危险（如多数人的暴政）。" },
                    { title: "通往奴役之路", originalTitle: "The Road to Serfdom", author: "弗里德里希·哈耶克", nationality: "奥地利裔英国人", year: 1944, recommendation: "新自由主义的代表作，认为任何形式的计划经济最终都会导向集权与奴役。" }
                ]
            },
            {
                name: "制度与创新",
                books: [
                    { title: "资本主义、社会主义与民主", originalTitle: "Capitalism, Socialism, and Democracy", author: "约瑟夫·熊彼特", nationality: "奥地利裔美国人", year: 1942, recommendation: "提出了“创造性破坏”的核心概念，认为资本主义的本质是企业家通过创新不断打破旧结构、创造新结构的过程。" },
                    { title: "制度、制度变迁与经济绩效", originalTitle: "Institutions, Institutional Change and Economic Performance", author: "道格拉斯·诺斯", nationality: "美国", year: 1990, recommendation: "新制度经济学的里程碑。论证了有效的制度（尤其是产权和法治）是实现国家富裕的关键。" },
                    { title: "财富、贫困与政治", originalTitle: "Wealth, Poverty and Politics", author: "托马斯·索维尔", nationality: "美国", year: 2015, recommendation: "用清晰的逻辑和翔实的数据，探讨了导致国家和族群之间财富差异的多种因素，破除了许多流行谬见。" }
                ]
            },
            {
                name: "反乌托邦警示",
                books: [
                    { title: "1984", originalTitle: "Nineteen Eighty-Four", author: "乔治·奥威尔", nationality: "英国", year: 1949, recommendation: "一部经典的政治讽刺小说，警示人们警惕权力对思想和自由的侵蚀。" },
                    { title: "美丽新世界", originalTitle: "Brave New World", author: "阿道司·赫胥黎", nationality: "英国", year: 1932, recommendation: "描绘了一个用科技和消费主义“麻醉”大众，从而剥夺其个性和自由的“幸福”社会。" }
                ]
            }
        ]
    };

    // Populate header and footer
    document.getElementById('main-title').textContent = bookData.title;
    document.getElementById('intro-quote').textContent = bookData.quote;
    document.getElementById('footer-text').textContent = bookData.footer;

    const bookListContainer = document.getElementById('book-list');

    // Generate book entries for each category
    bookData.categories.forEach(category => {
        const categorySection = document.createElement('section');
        categorySection.className = 'category';

        const categoryTitle = document.createElement('h2');
        categoryTitle.textContent = category.name;
        categorySection.appendChild(categoryTitle);

        category.books.forEach(book => {
            const entry = document.createElement('div');
            entry.className = 'book-entry';

            entry.innerHTML = `
                <h3>${book.title}</h3>
                <div class="book-meta">
                    <div><strong>著者:</strong> ${book.author}</div>
                    <div><strong>国籍:</strong> ${book.nationality}</div>
                    <div><strong>初版:</strong> ${book.year}年</div>
                </div>
                <div class="book-meta">
                    <div><strong>原名:</strong> <i>${book.originalTitle}</i></div>
                </div>
                <p class="recommendation">${book.recommendation}</p>
            `;

            categorySection.appendChild(entry);
        });

        bookListContainer.appendChild(categorySection);
    });

});